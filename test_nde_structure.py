import h5py
import json
import numpy as np

def analyze_nde_structure(file_path):
    """Analyser en détail la structure du fichier NDE problématique"""
    print(f"=== Analyse de {file_path} ===")
    
    with h5py.File(file_path, 'r') as f:
        print(f"Clés racine: {list(f.keys())}")
        
        # Vérifier Domain/Setup
        if 'Domain/Setup' in f:
            print("\n--- Domain/Setup trouvé ---")
            try:
                setup_data = f['Domain/Setup'][()]
                print(f"Type de données: {type(setup_data)}")
                print(f"Shape: {setup_data.shape if hasattr(setup_data, 'shape') else 'N/A'}")
                print(f"Dtype: {setup_data.dtype if hasattr(setup_data, 'dtype') else 'N/A'}")
                
                # Essayer de décoder comme JSON
                try:
                    if isinstance(setup_data, bytes):
                        json_str = setup_data.decode('utf-8')
                    else:
                        json_str = str(setup_data)
                    
                    json_data = json.loads(json_str)
                    print("✓ JSON décodé avec succès")
                    print(f"Clés JSON: {list(json_data.keys())}")
                    
                    if 'groups' in json_data:
                        print(f"Nombre de groupes: {len(json_data['groups'])}")
                        for i, group in enumerate(json_data['groups']):
                            print(f"  Groupe {i}: {list(group.keys())}")
                    
                except json.JSONDecodeError as e:
                    print(f"✗ Erreur décodage JSON: {e}")
                    print(f"Début des données: {str(setup_data)[:200]}...")
                    
            except Exception as e:
                print(f"Erreur lecture Domain/Setup: {e}")
        else:
            print("Domain/Setup n'existe pas")
        
        # Analyser Domain/DataGroups
        if 'Domain/DataGroups' in f:
            print("\n--- Domain/DataGroups trouvé ---")
            datagroups = f['Domain/DataGroups']
            print(f"Sous-groupes: {list(datagroups.keys())}")
            
            for group_key in datagroups.keys():
                group_path = f'Domain/DataGroups/{group_key}'
                print(f"\n  Groupe {group_key}:")
                group = f[group_path]
                print(f"    Clés: {list(group.keys())}")
                
                if 'Datasets' in group:
                    datasets = group['Datasets']
                    print(f"    Datasets: {list(datasets.keys())}")
                    
                    for dataset_key in datasets.keys():
                        dataset_path = f'{group_path}/Datasets/{dataset_key}'
                        dataset = f[dataset_path]
                        print(f"      Dataset {dataset_key}: {list(dataset.keys())}")
                        
                        # Chercher les données d'amplitude
                        if 'Amplitude' in dataset:
                            amp_data = dataset['Amplitude']
                            print(f"        Amplitude: shape={amp_data.shape}, dtype={amp_data.dtype}")
                            print(f"        Min/Max: {np.min(amp_data)}/{np.max(amp_data)}")
        
        # Analyser Applications/MXU
        if 'Applications' in f:
            print("\n--- Applications trouvé ---")
            apps = f['Applications']
            print(f"Sous-groupes: {list(apps.keys())}")
            
            if 'MXU' in apps:
                mxu = apps['MXU']
                print(f"  MXU clés: {list(mxu.keys())}")
                
                # Analyser les descripteurs
                if 'CscanDescriptor' in mxu:
                    cscan_desc = mxu['CscanDescriptor']
                    print(f"    CscanDescriptor: shape={cscan_desc.shape}, dtype={cscan_desc.dtype}")
                
                if 'AscanDescriptor' in mxu:
                    ascan_desc = mxu['AscanDescriptor']
                    print(f"    AscanDescriptor: shape={ascan_desc.shape}, dtype={ascan_desc.dtype}")

if __name__ == "__main__":
    # Utiliser le fichier NDE problématique
    file_path = "C:/Users/<USER>/Documents/datasets/Suzlon/SB70-MGO-153428-60M-65M-30-07-2025_1886.nde"
    analyze_nde_structure(file_path)
